<template>
  <q-page class="bg-grey-1">
    <div v-if="availableGamesDetails.length > 0"
         class="hero-section hero-gradient text-white text-center q-pa-xl"
         style="min-height:100vh; padding-bottom: 600px; box-sizing: border-box;">
      <LandingForRealtyGames :availableGamesDetails="availableGamesDetails"></LandingForRealtyGames>
    </div>
    <div v-else
         class="hero-section hero-gradient text-white text-center q-pa-xl"
         style="min-height:100vh; padding-bottom: 600px; box-sizing: border-box;">
      <LandingForDossier :scootFromLandingPage="scootFromLandingPage"
                         v-if="isDossier"></LandingForDossier>
      <LandingForPriceGuess :scootFromLandingPage="scootFromLandingPage"
                            v-if="isPriceGuess"></LandingForPriceGuess>
    </div>
  </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import useScoots from "src/compose/useScoots.js"
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import LandingForDossier from "src/components/landing/LandingForDossier.vue"
import LandingForPriceGuess from "src/components/landing/LandingForPriceGuess.vue"
import LandingForRealtyGames from "src/concerns/realty-game/components/landing/LandingForRealtyGames.vue"
export default defineComponent({
  name: 'PpsqSubdomainLandingPage',
  components: {
    LandingForDossier,
    LandingForPriceGuess,
    LandingForRealtyGames
  },
  methods: {
    // endSession() {
    //   localStorage.removeItem('userAccessCode');
    //   this.hasAccess = false;
    //   this.accessCode = '';
    // }
  },
  computed: {
    // dossierNickname() {
    //   const subdomainName = pwbFlexConfig.subdomainName;
    //   return "@" + subdomainName.charAt(0).toUpperCase() + subdomainName.slice(1);
    // }
  },
  // data() {
  //   return {
  //     isDossier: false,
  //   }
  // },
  setup() {
    const { getScootForRealtyGames, checkScoot } = useScoots()
    const $q = useQuasar();

    const scootFromLandingPage = ref(null);
    const isDossier = ref(false);
    const isPriceGuess = ref(false);
    const accessCode = ref('');
    const hasAccess = ref(false);
    const isLoading = ref(false);
    const errorMessage = ref('');
    const availableGamesDetails = ref([]);
    // const scootPrompt = ref('Please enter your access code to view the dossier.');

    const verifyAccessCode = async () => {
      if (!accessCode.value.trim()) {
        errorMessage.value = 'Please enter an access code.';
        return;
      }

      isLoading.value = true;
      errorMessage.value = '';

      try {
        const response = await checkScoot(pwbFlexConfig.subdomainName, accessCode.value);
        if (response.data.error) {
          errorMessage.value = response.data.error;
        } else {
          localStorage.setItem('userAccessCode', accessCode.value);
          hasAccess.value = true;
          accessCode.value = '';
          $q.notify({
            type: 'positive',
            message: 'Access granted!',
            position: 'top'
          });
        }
      } catch (error) {
        console.error('Verification failed:', error);
        errorMessage.value = error.message || 'Failed to verify access code.';
        $q.notify({
          type: 'negative',
          message: error.message || 'Failed to verify access code.',
          position: 'top'
        });
      } finally {
        isLoading.value = false;
      }
    };

    // const checkLocalStorageForAccess = () => {
    //   const storedAccessCode = localStorage.getItem('userAccessCode');
    //   if (storedAccessCode) {
    //     hasAccess.value = true;
    //     console.log('Access code found in local storage.');
    //   }
    // };

    const getInitialScoot = async () => {
      try {
        const response = await getScootForRealtyGames(pwbFlexConfig.subdomainName);
        scootFromLandingPage.value = response.data?.scoot;
        // if (response.data?.scoot?.scoot_notice) {
        //   scootPrompt.value = response.data.scoot.scoot_notice;
        // }
        if (response.data?.scoot?.available_games_details) {
          availableGamesDetails.value = response.data?.scoot?.available_games_details
        }
        if (response.data?.scoot?.is_price_guess_only) {
          isDossier.value = false
          isPriceGuess.value = true
          //  response.data.scoot.is_price_guess_only;
        }
        else {
          isPriceGuess.value = false
          isDossier.value = true
        }
        if (response.data.error) {
          errorMessage.value = response.data.error;
        }
      } catch (error) {
        console.error('Error fetching initial scoot:', error);
      }
    };

    onMounted(() => {
      // checkLocalStorageForAccess();
      getInitialScoot();
    });

    return {
      scootFromLandingPage,
      isPriceGuess,
      isDossier,
      // scootPrompt,
      getScootForRealtyGames,
      checkScoot,
      accessCode,
      hasAccess,
      isLoading,
      errorMessage,
      availableGamesDetails,
      verifyAccessCode,
    };
  },
});
</script>

<style>
/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
  opacity: 0.95;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%,
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 60px 60px;
}

.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.process-steps {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  margin-top: 3rem;
}

.process-step {
  flex: 1;
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.process-step:hover {
  transform: translateY(-10px);
}

.step-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: var(--q-primary);
  margin-bottom: 1rem;
  /* opacity: 0.2; */
}

.step-icon-container {
  background: #e3f2fd;
  border-radius: 50%;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.feature-card {
  border-radius: 1rem;
  transition: transform 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-icon-container {
  background: #e3f2fd;
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.feature-list {
  text-align: left;
}

.audience-card {
  height: 100%;
  border-radius: 1rem;
}

.audience-icon-container {
  background: #e3f2fd;
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.faq-list {
  border-radius: 1rem;
  overflow: hidden;
}

.cta-section {
  background: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
}

.animate-pop-in {
  animation: pop-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-bullets {
  list-style-type: none;
  padding-left: 0;
}

@media (max-width: 768px) {
  .process-steps {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-section {
    padding-bottom: 600px;
    /* Extra padding for mobile keyboard */
    box-sizing: border-box;
  }

  .q-page {
    min-height: 100vh;
    overflow-y: auto;
    /* Ensure scrollability */
  }
}
</style>