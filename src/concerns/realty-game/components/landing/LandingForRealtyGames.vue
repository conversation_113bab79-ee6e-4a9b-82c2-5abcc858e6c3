<template>
  <div class="max-ctr LandingForRealtyGames">
    <section class="hero">
      <div class="hero-overlay"></div>
      <div class="container row">
        <div class="hero-content col-12">
          <div class="justify-center flex">
            <h1 class="animate-pop-in hero-title">
              The {{ dossierNickname }} Property Price Challenge
            </h1>
          </div>
          <div class="subtitle animate-fade-in text-h4 q-mt-none q-mb-xl q-pb-lg">
            Time to show off your property price knowledge.
          </div>

          <!--  -->
          <div v-for="game in availableGamesDetails"
               :key="game.id"
               class="hero-cta">
            <q-btn :label="game.game_title"
                   color="yellow"
                   text-color="dark"
                   size="lg"
                   class="q-px-xl q-py-sm"
                   unelevated
                   :to="{ name: 'rPriceGameStart', params: { gameSlug: game.realty_game_slug } }" />

          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import useScoots from "src/compose/useScoots.js"
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default defineComponent({
  name: 'LandingForRealtyGames',
  methods: {
    endSession() {
      localStorage.removeItem('userAccessCode');
      this.hasAccess = false;
      this.accessCode = '';
    }
  },
  computed: {
    dossierNickname() {
      const subdomainName = pwbFlexConfig.subdomainName;
      return "@" + subdomainName.charAt(0).toUpperCase() + subdomainName.slice(1);
    }
  },
  setup() {
    const { getScoot, checkScoot } = useScoots()
    const $q = useQuasar();

    const accessCode = ref('');
    const hasAccess = ref(false);
    const isLoading = ref(false);
    const errorMessage = ref('');


    // const verifyAccessCode = async () => {
    //   if (!accessCode.value.trim()) {
    //     errorMessage.value = 'Please enter an access code.';
    //     return;
    //   }

    //   isLoading.value = true;
    //   errorMessage.value = '';

    //   try {
    //     const response = await checkScoot(pwbFlexConfig.subdomainName, accessCode.value);
    //     if (response.data.error) {
    //       errorMessage.value = response.data.error;
    //     } else {
    //       localStorage.setItem('userAccessCode', accessCode.value);
    //       hasAccess.value = true;
    //       accessCode.value = '';
    //       $q.notify({
    //         type: 'positive',
    //         message: 'Access granted!',
    //         position: 'top'
    //       });
    //     }
    //   } catch (error) {
    //     console.error('Verification failed:', error);
    //     errorMessage.value = error.message || 'Failed to verify access code.';
    //     $q.notify({
    //       type: 'negative',
    //       message: error.message || 'Failed to verify access code.',
    //       position: 'top'
    //     });
    //   } finally {
    //     isLoading.value = false;
    //   }
    // };

    // const checkLocalStorageForAccess = () => {
    //   const storedAccessCode = localStorage.getItem('userAccessCode');
    //   if (storedAccessCode) {
    //     hasAccess.value = true;
    //     console.log('Access code found in local storage.');
    //   }
    // };

    // const getInitialScoot = async () => {
    //   try {
    //     const response = await getScoot(pwbFlexConfig.subdomainName);
    //     if (response.data?.scoot?.scoot_notice) {
    //     }
    //     if (response.data.error) {
    //       errorMessage.value = response.data.error;
    //     }
    //   } catch (error) {
    //     console.error('Error fetching initial scoot:', error);
    //   }
    // };

    onMounted(() => {
      // checkLocalStorageForAccess();
      // getInitialScoot();
    });

    return {
      getScoot,
      checkScoot,
      accessCode,
      hasAccess,
      isLoading,
      errorMessage,
      // verifyAccessCode,
    };
  },
  props: {
    availableGamesDetails: {
      type: Array,
      // default: "GBP",
    },
  }
});
</script>

<style>
.process-steps {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  margin-top: 3rem;
}

.process-step {
  flex: 1;
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.process-step:hover {
  transform: translateY(-10px);
}

.step-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: var(--q-primary);
  margin-bottom: 1rem;
  /* opacity: 0.2; */
}

.step-icon-container {
  background: #e3f2fd;
  border-radius: 50%;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.feature-card {
  border-radius: 1rem;
  transition: transform 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-icon-container {
  background: #e3f2fd;
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.feature-list {
  text-align: left;
}

.audience-card {
  height: 100%;
  border-radius: 1rem;
}

.audience-icon-container {
  background: #e3f2fd;
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.faq-list {
  border-radius: 1rem;
  overflow: hidden;
}

.cta-section {
  background: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
}

.animate-pop-in {
  animation: pop-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-bullets {
  list-style-type: none;
  padding-left: 0;
}

@media (max-width: 768px) {
  .process-steps {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-section {
    padding-bottom: 600px;
    /* Extra padding for mobile keyboard */
    box-sizing: border-box;
  }

  .q-page {
    min-height: 100vh;
    overflow-y: auto;
    /* Ensure scrollability */
  }
}
</style>