import { ref, computed } from 'vue'

export function useRealtyGameStorage() {
  // Storage keys
  const STORAGE_KEYS = {
    SESSION_ID: 'price_guess_session_id',
    GUESSES: 'price_guess_guesses',
    SESSION_DATA: 'price_guess_session_data',
  }

  // Reactive state
  const currentSessionId = ref(null)
  const sessionGuesses = ref({})
  const sessionData = ref({})

  // Initialize from localStorage
  const initializeFromStorage = () => {
    try {
      // Load session ID
      const storedSessionId = localStorage.getItem(STORAGE_KEYS.SESSION_ID)
      if (storedSessionId) {
        currentSessionId.value = storedSessionId
      }

      // Load guesses
      const storedGuesses = localStorage.getItem(STORAGE_KEYS.GUESSES)
      if (storedGuesses) {
        sessionGuesses.value = JSON.parse(storedGuesses)
      }

      // Load session data
      const storedSessionData = localStorage.getItem(STORAGE_KEYS.SESSION_DATA)
      if (storedSessionData) {
        sessionData.value = JSON.parse(storedSessionData)
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error)
      // Reset storage if corrupted
      clearStorage()
    }
  }

  // Generate new session ID
  const generateSessionId = () => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `session_${timestamp}_${random}`
  }

  // Get or create session ID
  const getOrCreateSessionId = () => {
    if (!currentSessionId.value) {
      currentSessionId.value = generateSessionId()
      saveSessionId()
    }
    return currentSessionId.value
  }

  // Save session ID to localStorage
  const saveSessionId = () => {
    if (currentSessionId.value) {
      localStorage.setItem(STORAGE_KEYS.SESSION_ID, currentSessionId.value)
    }
  }

  // Save guess to localStorage
  const saveGuess = (propertyUuid, guessData) => {
    const sessionId = getOrCreateSessionId()

    if (!sessionGuesses.value[sessionId]) {
      sessionGuesses.value[sessionId] = {}
    }

    sessionGuesses.value[sessionId][propertyUuid] = {
      ...guessData,
      timestamp: Date.now(),
      sessionId: sessionId,
    }

    localStorage.setItem(
      STORAGE_KEYS.GUESSES,
      JSON.stringify(sessionGuesses.value)
    )
  }

  // Get guess for property
  const getGuess = (propertyUuid, sessionId = null) => {
    const targetSessionId = sessionId || currentSessionId.value
    if (!targetSessionId || !sessionGuesses.value[targetSessionId]) {
      return null
    }
    return sessionGuesses.value[targetSessionId][propertyUuid] || null
  }

  // Check if property has been guessed
  const hasGuessed = (propertyUuid, sessionId = null) => {
    return getGuess(propertyUuid, sessionId) !== null
  }

  // Get all guesses for current session
  const getCurrentSessionGuesses = () => {
    const sessionId = currentSessionId.value
    if (!sessionId || !sessionGuesses.value[sessionId]) {
      return {}
    }
    return sessionGuesses.value[sessionId]
  }

  // Save session metadata
  const saveSessionData = (data) => {
    const sessionId = getOrCreateSessionId()

    if (!sessionData.value[sessionId]) {
      sessionData.value[sessionId] = {}
    }

    sessionData.value[sessionId] = {
      ...sessionData.value[sessionId],
      ...data,
      lastUpdated: Date.now(),
    }

    localStorage.setItem(
      STORAGE_KEYS.SESSION_DATA,
      JSON.stringify(sessionData.value)
    )
  }

  // Get session metadata
  const getSessionData = (sessionId = null) => {
    const targetSessionId = sessionId || currentSessionId.value
    if (!targetSessionId || !sessionData.value[targetSessionId]) {
      return {}
    }
    return sessionData.value[targetSessionId]
  }

  // Clear all storage
  const clearStorage = () => {
    localStorage.removeItem(STORAGE_KEYS.SESSION_ID)
    localStorage.removeItem(STORAGE_KEYS.GUESSES)
    localStorage.removeItem(STORAGE_KEYS.SESSION_DATA)
    currentSessionId.value = null
    sessionGuesses.value = {}
    sessionData.value = {}
  }

  // Clear current session
  const clearCurrentSession = () => {
    const sessionId = currentSessionId.value
    if (sessionId) {
      // Remove from memory
      delete sessionGuesses.value[sessionId]
      delete sessionData.value[sessionId]

      // Update localStorage
      localStorage.setItem(
        STORAGE_KEYS.GUESSES,
        JSON.stringify(sessionGuesses.value)
      )
      localStorage.setItem(
        STORAGE_KEYS.SESSION_DATA,
        JSON.stringify(sessionData.value)
      )
    }

    // Reset current session
    currentSessionId.value = null
    localStorage.removeItem(STORAGE_KEYS.SESSION_ID)
  }

  // Start new session
  const startNewSession = () => {
    currentSessionId.value = generateSessionId()
    saveSessionId()
    return currentSessionId.value
  }

  // Get session statistics
  const getSessionStats = (sessionId = null) => {
    const targetSessionId = sessionId || currentSessionId.value
    const guesses = sessionGuesses.value[targetSessionId] || {}
    const guessCount = Object.keys(guesses).length

    let totalScore = 0
    let validGuesses = 0

    Object.values(guesses).forEach((guess) => {
      if (guess.score !== undefined) {
        totalScore += guess.score
        validGuesses++
      }
    })

    return {
      sessionId: targetSessionId,
      guessCount,
      totalScore,
      averageScore: validGuesses > 0 ? totalScore / validGuesses : 0,
      validGuesses,
    }
  }

  // Save selected currency for session
  const saveCurrencySelection = (currencyCode, sessionId = null) => {
    const targetSessionId = sessionId || getOrCreateSessionId()
    saveSessionData({ selectedCurrency: currencyCode })
  }

  // Get selected currency for session
  const getCurrencySelection = (sessionId = null) => {
    const sessionInfo = getSessionData(sessionId)
    return sessionInfo.selectedCurrency || 'GBP' // Default to GBP
  }

  // Computed properties
  const hasCurrentSession = computed(() => {
    return currentSessionId.value !== null
  })

  const currentSessionStats = computed(() => {
    return getSessionStats()
  })

  // Initialize on creation
  // initializeFromStorage()
  // Initialize on creation (client-only)
  if (typeof window !== 'undefined') {
    initializeFromStorage()
  }

  return {
    // State
    currentSessionId,
    sessionGuesses,
    sessionData,

    // Computed
    hasCurrentSession,
    currentSessionStats,

    // Methods
    initializeFromStorage,
    generateSessionId,
    getOrCreateSessionId,
    saveSessionId,
    saveGuess,
    getGuess,
    hasGuessed,
    getCurrentSessionGuesses,
    saveSessionData,
    getSessionData,
    clearStorage,
    clearCurrentSession,
    startNewSession,
    getSessionStats,
    saveCurrencySelection,
    getCurrencySelection,
  }
}
