import { ref, computed } from 'vue'
import currencyRatesData from '../data/currencyRates.json'

export function useCurrencyConverter() {
  // Reactive state
  const selectedCurrency = ref('GBP') // Default to GBP
  const currencies = ref(currencyRatesData.currencies)
  const exchangeRates = ref(currencyRatesData.exchangeRates)
  const supportedCurrencies = ref(currencyRatesData.supportedCurrencies)
  const isLoading = ref(false)
  const error = ref(null)

  // Computed properties
  const availableCurrencies = computed(() => {
    return supportedCurrencies.value
      .map((code) => ({
        code,
        name: currencies.value[code]?.name,
        symbol: currencies.value[code]?.symbol,
      }))
      .filter((item) => item.name)
  })

  const selectedCurrencyData = computed(() => {
    return currencies.value[selectedCurrency.value] || currencies.value.GBP
  })

  // Methods
  const convertPrice = (priceInCents, fromCurrency, toCurrency = null) => {
    if (!priceInCents || priceInCents === 0) return 0

    const targetCurrency = toCurrency || selectedCurrency.value

    // If same currency, no conversion needed
    if (fromCurrency === targetCurrency) return priceInCents

    // Get direct conversion rate
    const conversionRate = getConversionRate(fromCurrency, targetCurrency)
    const convertedPrice = priceInCents * conversionRate

    return Math.round(convertedPrice)
  }

  const formatPrice = (priceInCents, currency = null, options = {}) => {
    const targetCurrency = currency || selectedCurrency.value
    const currencyData = currencies.value[targetCurrency]

    if (!priceInCents || !currencyData) {
      return 'Price not available'
    }

    const amount = priceInCents / 100

    try {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency: targetCurrency,
        minimumFractionDigits: options.showDecimals ? 2 : 0,
        maximumFractionDigits: options.showDecimals ? 2 : 0,
      }).format(amount)
    } catch (e) {
      // Fallback formatting if Intl.NumberFormat fails
      return `${currencyData.symbol}${amount.toLocaleString('en-UK', {
        minimumFractionDigits: options.showDecimals ? 2 : 0,
        maximumFractionDigits: options.showDecimals ? 2 : 0,
      })}`
    }
  }

  const formatPriceWithBothCurrencies = (
    priceInCents,
    originalCurrency,
    showOriginalFirst = true,
    targetCurrency = null
  ) => {
    const secondCurrency = targetCurrency || selectedCurrency.value

    if (!priceInCents || secondCurrency === originalCurrency) {
      return formatPrice(priceInCents, originalCurrency)
    }

    const convertedPrice = convertPrice(
      priceInCents,
      originalCurrency,
      secondCurrency
    )
    const originalFormatted = formatPrice(priceInCents, originalCurrency)
    const convertedFormatted = formatPrice(convertedPrice, secondCurrency)

    if (showOriginalFirst) {
      return `${originalFormatted} (${convertedFormatted})`
    } else {
      return `${convertedFormatted} (${originalFormatted})`
    }
  }

  const getCurrencySymbol = (currency = null) => {
    const targetCurrency = currency || selectedCurrency.value
    return currencies.value[targetCurrency]?.symbol || '$'
  }

  const setCurrency = (currencyCode) => {
    if (currencies.value[currencyCode]) {
      selectedCurrency.value = currencyCode
    }
  }

  // API function to fetch live rates (optional enhancement)
  const fetchLiveRates = async () => {
    isLoading.value = true
    error.value = null

    try {
      // This would be implemented if you have a live currency API
      // For now, we'll just use the hardcoded rates
      console.log('Using hardcoded currency rates')
      return exchangeRates.value
    } catch (err) {
      error.value = 'Failed to fetch live currency rates'
      console.error('Currency rate fetch error:', err)
      // Fall back to hardcoded rates
      return exchangeRates.value
    } finally {
      isLoading.value = false
    }
  }

  // Validation helpers
  const isValidCurrency = (currencyCode) => {
    return !!currencies.value[currencyCode]
  }

  const getConversionRate = (fromCurrency, toCurrency) => {
    // If same currency, rate is 1
    if (fromCurrency === toCurrency) return 1

    // Check if we have a direct rate
    if (
      exchangeRates.value[fromCurrency] &&
      exchangeRates.value[fromCurrency][toCurrency]
    ) {
      return exchangeRates.value[fromCurrency][toCurrency]
    }

    // If no direct rate found, return 1 (no conversion)
    console.warn(
      `No conversion rate found from ${fromCurrency} to ${toCurrency}`
    )
    return 1
  }

  // Popular currencies for quick selection
  const popularCurrencies = computed(() => {
    const popular = ['GBP', 'USD', 'EUR', 'CAD', 'AUD', 'JPY']
    return popular
      .map((code) => ({
        code,
        name: currencies.value[code]?.name,
        symbol: currencies.value[code]?.symbol,
      }))
      .filter((item) => item.name)
  })

  return {
    // State
    selectedCurrency,
    currencies,
    exchangeRates,
    supportedCurrencies,
    isLoading,
    error,

    // Computed
    availableCurrencies,
    selectedCurrencyData,
    popularCurrencies,

    // Methods
    convertPrice,
    formatPrice,
    formatPriceWithBothCurrencies,
    getCurrencySymbol,
    setCurrency,
    fetchLiveRates,
    isValidCurrency,
    getConversionRate,
  }
}
