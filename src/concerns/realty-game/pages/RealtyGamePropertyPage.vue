<template>
  <div class="realty-game-property-page">
    <q-no-ssr>
      <div class="max-ctr q-pa-lg">
        <!-- Loading State -->
        <div v-if="isLoading"
             class="loading-container text-center q-pa-xl">
          <q-spinner color="primary"
                     size="3em" />
          <div class="q-mt-md text-h6">Loading Property...</div>
        </div>

        <!-- Error State -->
        <div v-else-if="error"
             class="error-container text-center q-pa-xl">
          <q-icon name="error"
                  color="negative"
                  size="3em" />
          <div class="q-mt-md text-h6 text-negative">Error Loading Property</div>
          <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
          <q-btn color="primary"
                 label="Go Back"
                 @click="$router.go(-1)" />
        </div>

        <!-- Property Content -->
        <div v-else-if="currentProperty"
             class="property-content">
          <!-- Property Header -->
          <div class="property-header q-mb-lg">
            <div class="row items-center justify-between">
              <div class="header-info">
                <h1 class="text-h4 text-weight-bold text-primary q-mb-xs">
                  {{ currentProperty.title }}
                </h1>
                <p class="text-body1 text-grey-7">
                  {{ currentProperty.city }}, {{ currentProperty.postal_code || currentProperty.region }}
                </p>
              </div>

              <div class="property-progress">
                <div class="text-subtitle2 text-grey-7">
                  Property {{ propertyIndex + 1 }} of {{ totalProperties }}
                </div>
                <q-linear-progress :value="(propertyIndex + 1) / totalProperties"
                                   color="primary"
                                   size="8px"
                                   rounded
                                   class="q-mt-xs"
                                   style="width: 200px;" />
              </div>
            </div>
          </div>

          <!-- Property Display -->
          <div class="row q-col-gutter-lg">
            <!-- Property Images and Details -->
            <div class="col-12 col-lg-8">
              <q-card class="property-card"
                      flat
                      bordered>
                <!-- Image Carousel -->
                <div class="m-guess-property-carousel"
                     style="height:100%">
                  <q-carousel v-model="currentImageIndex"
                              animated
                              arrows
                              navigation
                              infinite
                              height="400px"
                              class="rounded-borders">
                    <q-carousel-slide v-for="(image, index) in propertyImages"
                                      :key="index"
                                      :name="index"
                                      :img-src="image.image_details.url"
                                      class="carousel-slide">
                      <!-- <div class="absolute-bottom custom-caption">
                      <div class="text-subtitle2">{{ image.photo_title || `Photo ${index + 1}` }}</div>
                    </div> -->
                    </q-carousel-slide>
                  </q-carousel>
                </div>

                <!-- Property Details -->
                <q-card-section class="q-pa-lg">
                  <div class="property-features">
                    <div class="row q-col-gutter-md q-mb-lg">
                      <div class="col-auto"
                           v-if="currentProperty.count_bedrooms">
                        <div class="feature-item">
                          <q-icon name="bed"
                                  color="primary"
                                  size="sm"
                                  class="q-mr-sm" />
                          <span class="text-weight-medium">{{ currentProperty.count_bedrooms }} Bedrooms</span>
                        </div>
                      </div>
                      <div class="col-auto"
                           v-if="currentProperty.count_bathrooms">
                        <div class="feature-item">
                          <q-icon name="bathtub"
                                  color="primary"
                                  size="sm"
                                  class="q-mr-sm" />
                          <span class="text-weight-medium">{{ currentProperty.count_bathrooms }} Bathrooms</span>
                        </div>
                      </div>
                      <div class="col-auto"
                           v-if="currentProperty.count_garages">
                        <div class="feature-item">
                          <q-icon name="garage"
                                  color="primary"
                                  size="sm"
                                  class="q-mr-sm" />
                          <span class="text-weight-medium">{{ currentProperty.count_garages }} Garages</span>
                        </div>
                      </div>
                    </div>

                    <div v-if="currentProperty.description"
                         class="property-description">
                      <div class="text-subtitle2 text-weight-medium q-mb-sm">Description</div>
                      <div class="text-body2 text-grey-9"
                           v-html="currentProperty.description"></div>
                      <!-- <p class="text-body2 text-grey-9">
                      {{ currentProperty.description }}</p> -->
                    </div>
                  </div>
                  <q-no-ssr>
                    <div class="q-my-lg pgpp-leaflet-map-ctr"
                         v-if="currentProperty && currentProperty.latitude">
                      <PriceGuessLeafletMap :key="currentProperty?.uuid"
                                            :currentProperty="currentProperty" />
                    </div>
                    <div class="q-my-lg pgpp-boundary-map-ctr"
                         v-else-if="currentProperty && currentProperty.listing_geo_json">
                      <BoundaryLeafletMap :key="currentProperty?.uuid"
                                          :currentProperty="currentProperty">
                      </BoundaryLeafletMap>
                    </div>
                    <!-- <div class="q-my-lg pgpp-embed-map-ctr"
                       v-else-if="currentProperty && currentProperty.city">
                    <PriceGuessGoogleMapEmbed :key="currentProperty?.uuid"
                                              :embedLocation="currentProperty.city"></PriceGuessGoogleMapEmbed>
                  </div> -->
                  </q-no-ssr>

                </q-card-section>
              </q-card>
            </div>

            <!-- Guess Section -->
            <div class="col-12 col-lg-4">
              <q-card class="guess-card"
                      flat
                      bordered>
                <q-card-section class="q-pa-lg">
                  <!-- <div class="text-h6 text-weight-medium q-mb-md">
                  <q-icon name="attach_money"
                          color="primary"
                          class="q-mr-sm" />
                  What's Your Guess?
                </div>

                <div class="text-body2 text-grey-7 q-mb-lg">
                  Based on the photos and details, what do you think this property is worth?
                </div> -->

                  <div class="text-h6 text-weight-medium q-mb-md">
                    <q-icon name="attach_money"
                            color="primary"
                            class="q-mr-sm" />
                    <span v-if="!propertyAlreadyGuessed">What's Your Guess?</span>
                    <span v-else>Your Previous Guess</span>
                  </div>

                  <!-- Already guessed message -->
                  <div v-if="propertyAlreadyGuessed"
                       class="existing-guess-info q-mb-lg">
                    <q-banner class="bg-positive text-white rounded-borders">
                      <template v-slot:avatar>
                        <q-icon name="check_circle" />
                      </template>
                      <div class="text-body2">
                        You've made a guess for this property!
                      </div>
                      <div v-if="existingGuessData"
                           class="text-caption q-mt-xs">
                        Submitted on {{ new Date(existingGuessData.submittedAt).toLocaleDateString() }}
                        • Score: {{ existingGuessData.score }}/100
                      </div>
                    </q-banner>
                  </div>

                  <div v-else
                       class="text-body2 text-grey-7 q-mb-lg">
                    Based on the photos and details, what do you think this property is worth?
                  </div>

                  <!-- Price Input -->
                  <q-input v-model.number="userGuess"
                           type="number"
                           label="Your price estimate"
                           outlined
                           dense
                           :prefix="currencyPrefix"
                           placeholder="Enter your guess"
                           class="q-mb-md"
                           :error="validationErrors.length > 0"
                           :error-message="validationErrors.join(', ')"
                           @keyup.enter="handleSubmitGameGuess">
                    <template v-slot:append>
                      <q-icon name="help_outline"
                              color="grey-5">
                        <q-tooltip>Enter the amount you think this property is worth</q-tooltip>
                      </q-icon>
                    </template>
                  </q-input>

                  <!-- Human-Friendly Price Display -->
                  <div v-if="userGuess && userGuess > 0"
                       class="text-body2 text-grey-7 q-mb-md">
                    Your guess:
                    {{ formatPriceWithBothCurrencies((userGuess || 0) * 100, selectedCurrency,
                      true, currentProperty?.currency) }}
                  </div>

                  <!-- Existing guess details -->
                  <div v-if="propertyAlreadyGuessed && existingGuessData"
                       class="existing-guess-details q-mb-md">
                    <q-card flat
                            bordered
                            class="bg-grey-1">
                      <q-card-section class="q-pa-md">
                        <!-- <div class="text-subtitle2 text-weight-medium q-mb-sm">Your Result:</div> -->
                        <div>
                          <!-- <div class="result-row q-mb-md">
                            <div class="result-label">Your Guess:</div>
                            <div class="result-value text-weight-bold">
                              {{ formatPriceWithBothCurrencies((userGuess || 0) * 100, selectedCurrency,
                                true, currentProperty?.currency) }}
                            </div>
                          </div> -->

                          <div class="result-row q-mb-md">
                            <div class="result-label">Actual Price:</div>
                            <div class="result-value text-weight-bold">
                              {{ formatPriceWithBothCurrencies(currentProperty?.price_sale_current_cents,
                                currentProperty?.currency,
                                false) }}
                            </div>
                          </div>
                        </div>
                        <div class="row q-col-gutter-sm">
                          <div class="col-6">
                            <div class="text-caption text-grey-6">Score</div>
                            <div class="text-h6 text-weight-bold"
                                 :class="`text-${getScoreColor(existingGuessData.score)}`">
                              {{ existingGuessData.score }}/100
                            </div>
                          </div>
                          <div class="col-6">
                            <div class="text-caption text-grey-6">Difference</div>
                            <div class="text-body1 text-weight-medium"
                                 :class="existingGuessData.difference > 0 ? 'text-negative' : 'text-positive'">
                              {{ existingGuessData.difference > 0 ? '+' : '' }}{{
                                existingGuessData.difference?.toFixed(1)
                              }}%
                            </div>
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>

                  <!-- Submit Button -->
                  <q-btn v-if="!propertyAlreadyGuessed"
                         color="primary"
                         label="Submit Guess"
                         icon="send"
                         unelevated
                         rounded
                         size="lg"
                         class="full-width q-mb-md"
                         :disable="!userGuess || isSubmitting"
                         :loading="isSubmitting"
                         @click="handleSubmitGameGuess" />

                  <!-- View Results Button -->
                  <q-btn v-else
                         :label="isLastProperty ? 'View Results' : 'Next Property'"
                         color="secondary"
                         :icon="isLastProperty ? 'assessment' : 'arrow_forward'"
                         unelevated
                         rounded
                         size="lg"
                         class="full-width q-mb-md"
                         @click="handleNextProperty" />

                  <!-- Hint -->
                  <div v-if="!propertyAlreadyGuessed"
                       class="text-caption text-grey-6 text-center">
                    Take your time to study the property details
                  </div>
                  <div v-else
                       class="text-caption text-grey-6 text-center">
                    <!-- You can view your complete results or try another property -->
                  </div>


                  <!-- <q-btn flat
                       :label="isLastProperty ? 'View Results' : 'Next Property'"
                       color="primary"
                       :icon="isLastProperty ? 'flag' : 'arrow_forward'"
                       @click="handleNextProperty" /> -->
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </q-no-ssr>

    <!-- Feedback Dialog -->
    <q-dialog v-model="showFeedback"
              persistent>
      <q-card class="feedback-card"
              style="min-width: 400px;overflow-y: scroll;">
        <q-card-section class="feedback-header text-white"
                        :class="`bg-${getScoreColor(currentResult?.score || 0)}`">
          <div class="row items-center">
            <q-icon :name="currentResult?.score >= 80 ? 'celebration' : currentResult?.score >= 60 ? 'thumb_up' : 'info'"
                    size="md"
                    class="q-mr-md" />
            <div>
              <div class="text-h6">Guess Result</div>
              <div class="text-subtitle2">Score: {{ currentResult?.score || 0 }}/100</div>
            </div>
          </div>
        </q-card-section>

        <q-card-section class="q-pa-lg">
          <div class="result-details">
            <div class="result-row q-mb-md">
              <div class="result-label">Your Guess:</div>
              <div class="result-value text-weight-bold">
                {{ formatPriceWithBothCurrencies((currentResult?.guess || 0) * 100, currentProperty?.currency, false) }}
              </div>
            </div>

            <div class="result-row q-mb-md">
              <div class="result-label">Actual Price:</div>
              <div class="result-value text-weight-bold">
                {{ formatPriceWithBothCurrencies(currentProperty?.price_sale_current_cents, currentProperty?.currency,
                  false) }}
              </div>
            </div>

            <div class="result-row q-mb-md">
              <div class="result-label">Difference:</div>
              <div class="result-value"
                   :class="currentResult?.difference > 0 ? 'text-negative' : 'text-positive'">
                {{ currentResult?.difference > 0 ? '+' : '' }}{{ currentResult?.difference?.toFixed(1) || 0 }}%
              </div>
            </div>

            <q-separator class="q-my-md" />

            <div class="feedback-message text-center q-pa-md">
              <div class="text-body1">{{ currentResult?.feedback }}</div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right"
                        class="q-pa-lg">
          <q-btn flat
                 :label="isLastProperty ? 'View Results' : 'Next Property'"
                 color="primary"
                 :icon="isLastProperty ? 'flag' : 'arrow_forward'"
                 @click="handleNextProperty" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGame } from '../composables/useRealtyGame'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
// import PriceGuessGoogleMap from "src/concerns/price-guess/components/PriceGuessGoogleMap.vue"
import PriceGuessLeafletMap from "src/concerns/price-guess/components/PriceGuessLeafletMap.vue"
// import PriceGuessGoogleMapEmbed from "src/concerns/price-guess/components/PriceGuessGoogleMapEmbed.vue"
import BoundaryLeafletMap from "src/concerns/price-guess/components/BoundaryLeafletMap.vue"
// import BaseLeafletMap from 'src/concerns/maps/components/BaseLeafletMap.vue'

// Props
const props = defineProps({
  propertyIndex: {
    type: Number,
    required: true
  },
  gameSessionId: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update-progress', 'game-complete'])

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

// Get dossier UUID from route params
// const dossierUuid = computed(() => $route.params.dossierUuid)
const playerName = computed(() => $route.query.name || 'Anonymous Player')

// Initialize the price guess composable
const {
  isLoading,
  error,
  properties,
  totalProperties,
  getPropertyByIndex,
  formatPrice,
  getPropertyImages,
  validateGuess,
  submitGameGuess,
  getScoreColor,
  fetchPriceGuessData
} = useRealtyGame()

// Initialize the storage composable
const {
  saveGuess,
  getGuess,
  hasGuessed,
  getCurrencySelection
} = useRealtyGameStorage()

// Initialize the currency converter
const {
  selectedCurrency,
  setCurrency,
  formatPriceWithBothCurrencies,
  getCurrencySymbol,
  convertPrice
} = useCurrencyConverter()

// Local reactive state
const userGuess = ref(null)
const validationErrors = ref([])
const showFeedback = ref(false)
const currentResult = ref(null)
const currentImageIndex = ref(0)
const isSubmitting = ref(false)

// Computed properties
const currentProperty = computed(() => {
  let gameListingContainer = getPropertyByIndex(props.propertyIndex)
  return gameListingContainer?.listing_details || {}
})

const propertyImages = computed(() => {
  let allImages = currentProperty.value ? getPropertyImages(currentProperty.value) : []
  return allImages.filter(image => !image.flag_is_hidden)
})

// filteredImages() {
//       return this.propertyImages.filter(image => !image.flag_is_hidden);
//     },

const isLastProperty = computed(() => {
  return props.propertyIndex >= totalProperties.value - 1
})

// Initialize currency from session
const sessionCurrency = getCurrencySelection(props.gameSessionId)
if (sessionCurrency) {
  setCurrency(sessionCurrency)
}

const currencyPrefix = computed(() => {
  return getCurrencySymbol()
})

// Check if this property has already been guessed
const propertyAlreadyGuessed = computed(() => {
  if (!currentProperty.value) return false
  return hasGuessed(currentProperty.value.uuid, props.gameSessionId)
})

// Get existing guess data
const existingGuessData = computed(() => {
  if (!currentProperty.value) return null
  return getGuess(currentProperty.value.uuid, props.gameSessionId)
})

// Methods
const validateCurrentGuess = () => {
  // Always clear previous errors first
  validationErrors.value = []

  if (!currentProperty.value) return false

  const actualPrice = currentProperty.value.price_sale_current_cents / 100
  const validation = validateGuess(userGuess.value, actualPrice)

  if (!validation.isValid) {
    validationErrors.value = validation.errors
    return false
  }
  return true
}

const handleSubmitGameGuess = async () => {
  // Check if already guessed
  if (propertyAlreadyGuessed.value) {
    $q.notify({
      color: 'warning',
      message: 'You have already made a guess for this property',
      icon: 'warning',
      position: 'top'
    })
    return
  }

  // Validate only on submit
  const isValid = validateCurrentGuess()
  if (!isValid) {
    return
  }

  isSubmitting.value = true

  try {
    let realtyGameSlug = $route.params.gameSlug
    let userGuessInUiCurrency = userGuess.value
    let uiCurrency = selectedCurrency.value //|| "USD"
    let userGuessInListingCurrency = convertPrice(userGuess.value, uiCurrency, currentProperty.value.currency)
    const result = await submitGameGuess(userGuessInListingCurrency, {
      realtyGameSlug: realtyGameSlug,
      userGuessInUiCurrency: userGuessInUiCurrency,
      uiCurrency: uiCurrency,
      name: playerName.value,
      sessionId: props.gameSessionId,
      userUuid: null,
      // scootUuid: dossierUuid.value
    }, currentProperty.value, props.propertyIndex)

    if (result.success) {
      // Save to local storage
      const guessData = {
        guess: userGuess.value,
        score: result.result.score,
        difference: result.result.difference,
        feedback: result.result.feedback,
        actualPrice: result.result.actualPrice,
        propertyUuid: currentProperty.value.uuid,
        propertyTitle: currentProperty.value.title,
        currency: currentProperty.value.currency,
        playerName: playerName.value,
        submittedAt: new Date().toISOString(),
        sessionId: props.gameSessionId
      }

      saveGuess(currentProperty.value.uuid, guessData)

      currentResult.value = result.result
      showFeedback.value = true

      if (result.saveError) {
        $q.notify({
          color: 'warning',
          message: result.saveError,
          icon: 'warning',
          position: 'top'
        })
      }
      // Reset guess input
      userGuess.value = null
      $q.notify({
        color: getScoreColor(result.result.score),
        message: `Score: ${result.result.score}/100`,
        icon: result.result.score >= 80 ? 'celebration' : 'info',
        position: 'top'
      })
    } else {
      validationErrors.value = result.errors
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'Failed to submit guess. Please try again.',
      icon: 'error',
      position: 'top'
    })
  } finally {
    isSubmitting.value = false
  }
}

const handleNextProperty = () => {
  showFeedback.value = false

  if (isLastProperty.value) {
    // Navigate to results page
    $router.push({
      name: 'rPriceGameResults',
      params: {
        // dossierUuid: dossierUuid.value,
        gameSessionId: props.gameSessionId
      }
    })
  } else {
    // Navigate to next property
    $router.push({
      name: 'rPriceGuessProperty',
      params: {
        // dossierUuid: dossierUuid.value,
        propertyIndex: props.propertyIndex + 1
      },
      query: {
        session: props.gameSessionId,
        ...(playerName.value !== 'Anonymous Player' && { name: playerName.value })
      }
    })
  }
}

// Watch for property changes to emit progress
watch([() => props.propertyIndex, totalProperties], ([newIndex, total]) => {
  if (total > 0) {
    emit('update-progress', {
      currentIndex: newIndex,
      total: total
    })
  }
}, { immediate: true })

// Initialize data if not loaded
const initializeData = async () => {
  if (!properties.value || properties.value.length === 0) {
    try {
      await fetchPriceGuessData()
    } catch (err) {
      console.error('Failed to load property data:', err)
    }
  }
}

// Load existing guess if available
const loadExistingGuess = () => {
  if (currentProperty.value && propertyAlreadyGuessed.value) {
    const existingData = existingGuessData.value
    if (existingData) {
      userGuess.value = existingData.guess
    }
  }
}

// Initialize on mount
onMounted(async () => {
  // Load data if needed
  await initializeData()

  // Load existing guess if available
  loadExistingGuess()

  // Reset form state (but preserve existing guess)
  if (!propertyAlreadyGuessed.value) {
    userGuess.value = null
  }
  validationErrors.value = []
  showFeedback.value = false
  currentResult.value = null
  currentImageIndex.value = 0
})
</script>

<style scoped>
.realty-game-property-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.property-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.property-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.m-guess-property-carousel {
  height: 400px;
}

.carousel-slide {
  background-size: cover;
  background-position: center;
}

.custom-caption {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.guess-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 120px;
}

.feedback-card {
  border-radius: 12px;
  overflow: hidden;
}

.feedback-header {
  padding: 1.5rem;
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.result-label {
  font-weight: 500;
  color: #666;
}

.result-value {
  font-size: 1.1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .property-header .row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .property-progress {
    width: 100%;
  }

  .property-progress .q-linear-progress {
    width: 100% !important;
  }

  .m-guess-property-carousel {
    height: 300px;
  }

  .guess-card {
    position: static;
    margin-top: 1rem;
  }
}
</style>