<template>
  <div class="price-guess-create-game">
    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-lg">
        <div>
          <h4 class="q-my-none">Create New Price Guess Game</h4>
          <p class="text-grey-6 q-mb-none">
            Set up a new price guessing game with custom settings
          </p>
        </div>
        <div>
          <q-btn flat
                 color="grey-7"
                 icon="arrow_back"
                 label="Back to Admin"
                 @click="$router.push({ name: 'rPriceGuessPropertiesAdmin' })" />
        </div>
      </div>

      <div class="max-width-form">
        <q-form @submit="createGame"
                class="q-gutter-md">
          <!-- Game Title -->
          <q-card class="q-pa-md">
            <q-card-section>
              <h6 class="q-mt-none q-mb-sm">Game Information</h6>

              <q-input v-model="gameForm.title"
                       label="Game Title *"
                       outlined
                       :rules="[val => !!val || 'Title is required']"
                       hint="Enter a catchy title for your price guessing game" />

              <q-input v-model="gameForm.description"
                       label="Game Description"
                       type="textarea"
                       outlined
                       rows="3"
                       class="q-mt-md"
                       hint="Provide a brief description of the game (optional)" />
            </q-card-section>
          </q-card>

          <!-- Game Schedule -->
          <q-card class="q-pa-md">
            <q-card-section>
              <h6 class="q-mt-none q-mb-sm">Game Schedule</h6>

              <div class="row q-gutter-md">
                <div class="col-12 col-md-6">
                  <q-input v-model="gameForm.startDate"
                           label="Start Date *"
                           type="date"
                           outlined
                           :rules="[val => !!val || 'Start date is required']" />
                </div>
                <div class="col-12 col-md-6">
                  <q-input v-model="gameForm.startTime"
                           label="Start Time *"
                           type="time"
                           outlined
                           :rules="[val => !!val || 'Start time is required']" />
                </div>
              </div>

              <div class="row q-gutter-md q-mt-sm">
                <div class="col-12 col-md-6">
                  <q-input v-model="gameForm.endDate"
                           label="End Date *"
                           type="date"
                           outlined
                           :rules="[val => !!val || 'End date is required', validateEndDate]" />
                </div>
                <div class="col-12 col-md-6">
                  <q-input v-model="gameForm.endTime"
                           label="End Time *"
                           type="time"
                           outlined
                           :rules="[val => !!val || 'End time is required']" />
                </div>
              </div>

              <div class="q-mt-md">
                <q-banner v-if="scheduleSummary"
                          class="bg-blue-1 text-blue-8">
                  <template v-slot:avatar>
                    <q-icon name="schedule" />
                  </template>
                  {{ scheduleSummary }}
                </q-banner>
              </div>
            </q-card-section>
          </q-card>

          <!-- Game Settings -->
          <q-card class="q-pa-md">
            <q-card-section>
              <h6 class="q-mt-none q-mb-sm">Game Settings</h6>

              <q-toggle v-model="gameForm.isActive"
                        label="Activate game immediately"
                        color="positive"
                        class="q-mb-md" />

              <q-toggle v-model="gameForm.allowAnonymous"
                        label="Allow anonymous players"
                        color="primary"
                        class="q-mb-md" />

              <q-input v-model.number="gameForm.maxPlayers"
                       label="Maximum Players (optional)"
                       type="number"
                       outlined
                       min="1"
                       hint="Leave empty for unlimited players" />
            </q-card-section>
          </q-card>

          <!-- Action Buttons -->
          <div class="row justify-end q-gutter-sm q-mt-lg">
            <q-btn flat
                   color="grey-7"
                   label="Cancel"
                   @click="$router.push({ name: 'rPriceGuessPropertiesAdmin' })" />
            <q-btn type="submit"
                   color="primary"
                   label="Create Game"
                   icon="add"
                   :loading="isCreating"
                   :disable="!isFormValid" />
          </div>
        </q-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { usePriceGuess } from '../composables/usePriceGuess'

const $router = useRouter()
const $q = useQuasar()

// Initialize the price guess composable
const { createPriceGuessGame } = usePriceGuess()

// Form data
const gameForm = ref({
  title: '',
  description: '',
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  isActive: false,
  allowAnonymous: true,
  maxPlayers: null
})

// Loading state
const isCreating = ref(false)

// Computed properties
const isFormValid = computed(() => {
  return gameForm.value.title &&
    gameForm.value.startDate &&
    gameForm.value.startTime &&
    gameForm.value.endDate &&
    gameForm.value.endTime
})

const scheduleSummary = computed(() => {
  if (!gameForm.value.startDate || !gameForm.value.endDate) return ''

  const startDateTime = new Date(`${gameForm.value.startDate}T${gameForm.value.startTime}`)
  const endDateTime = new Date(`${gameForm.value.endDate}T${gameForm.value.endTime}`)

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) return ''

  const duration = endDateTime - startDateTime
  const days = Math.floor(duration / (1000 * 60 * 60 * 24))
  const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

  let summary = `Game runs from ${startDateTime.toLocaleDateString()} at ${gameForm.value.startTime} to ${endDateTime.toLocaleDateString()} at ${gameForm.value.endTime}`

  if (days > 0) {
    summary += ` (${days} day${days > 1 ? 's' : ''}`
    if (hours > 0) summary += ` and ${hours} hour${hours > 1 ? 's' : ''})`
    else summary += ')'
  } else if (hours > 0) {
    summary += ` (${hours} hour${hours > 1 ? 's' : ''})`
  }

  return summary
})

// Validation functions
const validateEndDate = (val) => {
  if (!val || !gameForm.value.startDate) return true

  const startDateTime = new Date(`${gameForm.value.startDate}T${gameForm.value.startTime || '00:00'}`)
  const endDateTime = new Date(`${val}T${gameForm.value.endTime || '23:59'}`)

  return endDateTime > startDateTime || 'End date must be after start date'
}

// Methods
const createGame = async () => {
  isCreating.value = true

  try {
    // Combine date and time for API
    const startDateTime = new Date(`${gameForm.value.startDate}T${gameForm.value.startTime}`)
    const endDateTime = new Date(`${gameForm.value.endDate}T${gameForm.value.endTime}`)

    const gameData = {
      title: gameForm.value.title,
      description: gameForm.value.description,
      start_time: startDateTime.toISOString(),
      end_time: endDateTime.toISOString(),
      is_active: gameForm.value.isActive,
      allow_anonymous: gameForm.value.allowAnonymous,
      max_players: gameForm.value.maxPlayers || null
    }

    // Create the game using the API
    await createPriceGuessGame(gameData)

    $q.notify({
      color: 'positive',
      message: 'Game created successfully!',
      icon: 'check'
    })

    // Navigate back to admin page
    $router.push({ name: 'rPriceGuessPropertiesAdmin' })

  } catch (error) {
    console.error('Error creating game:', error)
    $q.notify({
      color: 'negative',
      message: 'Failed to create game. Please try again.',
      icon: 'error'
    })
  } finally {
    isCreating.value = false
  }
}

// Set default dates (today and tomorrow)
const initializeDefaultDates = () => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  gameForm.value.startDate = today.toISOString().split('T')[0]
  gameForm.value.startTime = '09:00'
  gameForm.value.endDate = tomorrow.toISOString().split('T')[0]
  gameForm.value.endTime = '17:00'
}

// Initialize form with default values
initializeDefaultDates()
</script>

<style scoped>
.price-guess-create-game {
  max-width: 1200px;
  margin: 0 auto;
}

.max-width-form {
  max-width: 800px;
  margin: 0 auto;
}
</style>
