<template>
  <q-layout view="lHh Lpr lFf">
    <q-page-container>

      <q-page padding>
        <!-- Search Form Card -->
        <q-card class="q-mb-lg">
          <q-card-section>
            <div class="text-h6">Property Search</div>
            <div class="text-subtitle2">Enter your search criteria</div>
          </q-card-section>

          <q-card-section>
            <q-form @submit="searchProperties"
                    class="q-gutter-md">
              <div class="row q-col-gutter-md">
                <!-- Search Type -->
                <div class="col-12 col-md-6">
                  <q-select filled
                            v-model="searchParams.searchType"
                            :options="searchTypeOptions"
                            label="Search Type"
                            emit-value
                            map-options
                            dense />
                </div>

                <!-- Location ID -->
                <div class="col-12 col-md-6">
                  <q-input filled
                           v-model="searchParams.locationId"
                           label="Location ID (e.g., b13-9xp)"
                           hint="Use a postcode or area identifier from the website"
                           dense
                           lazy-rules
                           :rules="[val => !!val || 'Location is required']" />
                </div>

                <!-- Min Price -->
                <div class="col-6 col-md-3">
                  <q-input filled
                           v-model.number="searchParams.minPrice"
                           type="number"
                           label="Min Price"
                           prefix="£"
                           dense />
                </div>

                <!-- Max Price -->
                <div class="col-6 col-md-3">
                  <q-input filled
                           v-model.number="searchParams.maxPrice"
                           type="number"
                           label="Max Price"
                           prefix="£"
                           dense />
                </div>

                <!-- Min Bedrooms -->
                <div class="col-12 col-md-6">
                  <q-select filled
                            v-model="searchParams.minBedrooms"
                            :options="bedroomOptions"
                            label="Min Bedrooms"
                            emit-value
                            map-options
                            dense />
                </div>
              </div>

              <q-card-actions align="right">
                <q-btn label="Search"
                       type="submit"
                       color="primary"
                       icon="search" />
              </q-card-actions>
            </q-form>
          </q-card-section>
        </q-card>

        <!-- Loading, Error and Results Section -->
        <div class="results-container">
          <!-- Loading Spinner -->
          <div v-if="isLoading"
               class="text-center">
            <q-spinner-dots color="primary"
                            size="3em" />
            <p class="q-mt-md">Fetching properties...</p>
          </div>

          <!-- Error Message -->
          <q-banner v-if="error"
                    inline-actions
                    class="text-white bg-red rounded-borders">
            <template v-slot:avatar>
              <q-icon name="error"
                      color="white" />
            </template>
            {{ error }}
            <q-tooltip>
              <div class="q-pa-sm"
                   style="max-width: 300px">
                <strong>CORS Policy Error:</strong> Modern browsers block web pages from making API requests to a
                different
                domain.
                To fix this, you need a server-side proxy. This is expected behavior.
              </div>
            </q-tooltip>
          </q-banner>

          <!-- No Results Found -->
          <q-card v-if="!isLoading && properties.length === 0 && hasSearched"
                  flat
                  bordered
                  class="text-center q-pa-lg">
            <q-icon name="house"
                    size="4em"
                    color="grey-5" />
            <div class="text-h6 q-mt-md text-grey-7">No Properties Found</div>
            <p class="text-grey-7">Try adjusting your search criteria.</p>
          </q-card>


          <!-- Results Grid -->
          <div v-if="properties.length > 0"
               class="row q-col-gutter-lg">
            <div v-for="property in properties"
                 :key="property.id"
                 class="col-12 col-sm-6 col-md-4 col-lg-3">
              <q-card class="property-card">
                <q-img :src="property['cover-images'].webp || property['cover-images'].default">
                  <q-badge color="secondary"
                           floating
                           class="q-pa-xs">
                    {{ property.price }}
                  </q-badge>
                </q-img>

                <q-card-section>
                  <div class="text-overline text-orange-9">{{ property['price-qualifier'] }}</div>
                  <div class="text-h6 q-mt-sm q-mb-xs ellipsis">{{ property['property-title'] }}</div>
                  <div class="text-caption text-grey ellipsis">
                    {{ property['display_address'] }}
                  </div>
                </q-card-section>

                <q-card-section class="q-pt-none">
                  <q-chip dense
                          icon="hotel"
                          :label="`${property.bedrooms} Bed${property.bedrooms > 1 ? 's' : ''}`" />
                  <q-chip v-if="property.bathrooms"
                          dense
                          icon="bathtub"
                          :label="`${property.bathrooms} Bath${property.bathrooms > 1 ? 's' : ''}`" />
                </q-card-section>

                <q-separator />

                <q-card-actions align="right">
                  <q-btn flat
                         color="primary"
                         label="View Details"
                         :href="`${websiteUrlPrefix}${property['property-link']}`"
                         target="_blank"
                         icon-right="open_in_new" />
                </q-card-actions>
              </q-card>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

// --- Reactive State ---

// Form parameters with defaults from the example URL
const searchParams = ref({
  searchType: 'for-sale',
  locationId: 'b13-9xp',
  minPrice: 690000,
  maxPrice: 10500000,
  minBedrooms: 2
});

// Options for select inputs
const searchTypeOptions = [
  { label: 'For Sale', value: 'for-sale' },
  { label: 'To Rent', value: 'to-rent' }
];
const bedroomOptions = [
  { label: 'Any', value: null },
  { label: '1+', value: 1 },
  { label: '2+', value: 2 },
  { label: '3+', value: 3 },
  { label: '4+', value: 4 },
  { label: '5+', value: 5 }
];

// State for API call and results
const properties = ref([]);
const isLoading = ref(false);
const error = ref(null);
const websiteUrlPrefix = ref('');
const hasSearched = ref(false); // To track if a search has been attempted

// --- Methods ---

const searchProperties = async () => {
  // Reset state
  properties.value = [];
  isLoading.value = true;
  error.value = null;
  hasSearched.value = true;

  // Base URL for the API
  const baseUrl = 'https://www.onthemarket.com/async/search/properties/';

  // Build query parameters dynamically
  const query = new URLSearchParams();

  // Add params only if they have a value to create a clean URL
  if (searchParams.value.searchType) query.append('search-type', searchParams.value.searchType);
  if (searchParams.value.locationId) query.append('location-id', searchParams.value.locationId);
  if (searchParams.value.minPrice) query.append('min-price', searchParams.value.minPrice);
  if (searchParams.value.maxPrice) query.append('max-price', searchParams.value.maxPrice);
  if (searchParams.value.minBedrooms) query.append('min-bedrooms', searchParams.value.minBedrooms);

  const finalUrl = `${baseUrl}?${query.toString()}`;
  console.log('Requesting URL:', finalUrl);

  try {
    //
    // !!! IMPORTANT !!!
    // This fetch call will fail in the browser due to CORS policy.
    // In a real application, you would call your own backend proxy here.
    // e.g., const response = await fetch(`/api/properties?${query.toString()}`);
    //
    const response = await fetch(finalUrl);

    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data && data.properties && Array.isArray(data.properties)) {
      properties.value = data.properties;
      websiteUrlPrefix.value = data['website-url-prefix'] || 'https://www.onthemarket.com'; // Fallback
      if (data.properties.length === 0) {
        $q.notify({
          color: 'info',
          icon: 'info',
          message: 'Your search returned 0 properties.'
        })
      }
    } else {
      // Handle cases where the API returns a 200 OK but no properties array
      throw new Error('Invalid data structure received from API.');
    }

  } catch (e) {
    console.error('Failed to fetch properties:', e);
    // This is the error message the user will most likely see.
    error.value = 'Failed to fetch properties. This is likely due to a CORS policy error. Check the console for details.';
    $q.notify({
      type: 'negative',
      message: 'An error occurred during the search.'
    });
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.property-card {
  // Ensures cards have the same height in a row
  height: 100%;
  display: flex;
  flex-direction: column;

  .q-card__section {
    flex-grow: 1;
  }
}
</style>