<template>
  <div class="price-guess-properties-admin">
    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-lg">
        <div>
          <h4 class="q-my-none">Price Guess Game - Property Management</h4>
          <p class="text-grey-6 q-mb-none">
            Manage property visibility and titles for the price guessing game
          </p>
        </div>
        <div class="row q-gutter-sm">
          <q-btn color="positive"
                 icon="add"
                 label="Create New Game"
                 @click="$router.push({ name: 'rPriceGuessCreateGame' })" />
          <q-btn color="primary"
                 icon="refresh"
                 label="Refresh Data"
                 @click="refreshData"
                 :loading="isLoading" />
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading"
           class="text-center q-py-xl">
        <q-spinner-dots size="50px"
                        color="primary" />
        <p class="q-mt-md text-grey-6">Loading properties...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="text-center q-py-xl">
        <q-icon name="error"
                size="50px"
                color="negative" />
        <p class="q-mt-md text-negative">{{ error }}</p>
        <q-btn color="primary"
               @click="refreshData"
               label="Try Again" />
      </div>

      <!-- Properties List -->
      <div v-else-if="properties.length > 0">
        <div class="q-mb-md">
          <q-badge color="info"
                   :label="`${properties.length} Properties`" />
        </div>

        <div class="properties-grid">
          <q-card v-for="(property, index) in properties"
                  :key="property.uuid"
                  class="property-card q-mb-md"
                  :class="{ 'property-hidden': !property.visible }">
            <q-card-section>
              <div class="row items-start justify-between">
                <div class="col-8">
                  <div class="property-header q-mb-sm">
                    <q-badge :color="property.visible ? 'positive' : 'negative'"
                             :label="property.visible ? 'Visible' : 'Hidden'"
                             class="q-mr-sm" />
                    <span class="text-caption text-grey-6">
                      Property {{ index + 1 }}
                    </span>
                  </div>

                  <!-- Editable Title -->
                  <div class="property-title q-mb-sm">
                    <q-input v-model="property.title"
                             label="Property Title"
                             outlined
                             dense
                             @blur="updatePropertyTitle(property)"
                             :loading="property._updating" />
                  </div>

                  <div class="property-details text-caption text-grey-6">
                    <div>{{ property.formatted_display_price }}</div>
                    <div>{{ property.street_address }}, {{ property.city }}</div>
                    <div>{{ property.count_bedrooms }} bed, {{ property.count_bathrooms }} bath</div>
                  </div>
                </div>

                <div class="col-4 text-right">
                  <!-- Property Visibility Toggle -->
                  <q-toggle v-model="property.visible"
                            color="positive"
                            size="lg"
                            @update:model-value="updatePropertyVisibility(property)"
                            :loading="property._updating" />
                </div>
              </div>
            </q-card-section>

            <!-- Property Images -->
            <q-card-section v-if="property.sale_listing_pics?.length > 0">
              <div class="images-header q-mb-sm">
                <h6 class="q-my-none">Property Images ({{ property.sale_listing_pics.length }})</h6>
                <p class="text-caption text-grey-6 q-mb-none">
                  Toggle individual image visibility
                </p>
              </div>

              <div class="images-grid">
                <div v-for="(image, imgIndex) in property.sale_listing_pics"
                     :key="image.uuid"
                     class="image-item"
                     :class="{ 'image-hidden': image.flag_is_hidden }">
                  <div class="image-container">
                    <img :src="image.image_details?.small_fit?.url || image.photo_slug"
                         :alt="image.photo_title"
                         class="property-image" />
                    <div class="image-overlay">
                      <q-toggle :model-value="!image.flag_is_hidden"
                                @update:model-value="updateImageVisibility(property, image, !$event)"
                                color="white"
                                size="sm"
                                :loading="image._updating" />
                    </div>
                  </div>
                  <div class="image-info q-mt-xs">
                    <div class="text-caption font-weight-medium">
                      {{ image.photo_title || `Image ${imgIndex + 1}` }}
                    </div>
                    <div class="text-caption text-grey-6">
                      {{ image.flag_is_hidden ? 'Hidden' : 'Visible' }}
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else
           class="text-center q-py-xl">
        <q-icon name="home"
                size="50px"
                color="grey-5" />
        <p class="q-mt-md text-grey-6">No properties found</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { usePriceGuess } from '../composables/usePriceGuess'

const $q = useQuasar()

// Initialize the price guess composable
const {
  isLoading,
  error,
  properties,
  fetchPriceGuessData,
  updatePropertyVisibility: apiUpdatePropertyVisibility,
  updatePropertyTitle: apiUpdatePropertyTitle,
  updateImageVisibility: apiUpdateImageVisibility
} = usePriceGuess()

// Local state for tracking updates
const updatingProperties = ref(new Set())
const updatingImages = ref(new Set())

// Methods
const refreshData = async () => {
  try {
    await fetchPriceGuessData()
    $q.notify({
      color: 'positive',
      message: 'Properties refreshed successfully',
      icon: 'check'
    })
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to refresh properties',
      icon: 'error'
    })
  }
}

const updatePropertyVisibility = async (property) => {
  // Set updating state
  property._updating = true

  try {
    await apiUpdatePropertyVisibility(property.uuid, property.visible)

    $q.notify({
      color: 'positive',
      message: `Property ${property.visible ? 'shown' : 'hidden'} in game`,
      icon: property.visible ? 'visibility' : 'visibility_off'
    })
  } catch (err) {
    // Revert the change on error
    property.visible = !property.visible
    $q.notify({
      color: 'negative',
      message: 'Failed to update property visibility',
      icon: 'error'
    })
  } finally {
    property._updating = false
  }
}

const updatePropertyTitle = async (property) => {
  property._updating = true

  try {
    await apiUpdatePropertyTitle(property.uuid, property.title)

    $q.notify({
      color: 'positive',
      message: 'Property title updated',
      icon: 'edit'
    })
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to update property title',
      icon: 'error'
    })
  } finally {
    property._updating = false
  }
}

const updateImageVisibility = async (property, image, isHidden) => {
  image._updating = true

  try {
    // Update the flag
    image.flag_is_hidden = isHidden

    await apiUpdateImageVisibility(image.uuid, isHidden)

    $q.notify({
      color: 'positive',
      message: `Image ${isHidden ? 'hidden' : 'shown'} in game`,
      icon: isHidden ? 'visibility_off' : 'visibility'
    })
  } catch (err) {
    // Revert the change on error
    image.flag_is_hidden = !isHidden
    $q.notify({
      color: 'negative',
      message: 'Failed to update image visibility',
      icon: 'error'
    })
  } finally {
    image._updating = false
  }
}

// Initialize data on mount
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.price-guess-properties-admin {
  max-width: 1200px;
  margin: 0 auto;
}

.property-card {
  transition: opacity 0.3s ease;
}

.property-hidden {
  opacity: 0.6;
}

.property-header {
  display: flex;
  align-items: center;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.image-item {
  transition: opacity 0.3s ease;
}

.image-hidden {
  opacity: 0.5;
}

.image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.property-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
}

.image-info {
  text-align: center;
}
</style>
