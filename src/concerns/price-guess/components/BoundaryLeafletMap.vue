<template>
  <div class="q-pa-none">
    <q-card class="property-map-card">
      <q-card-section class="q-pa-none">
        <!--
          This container will be rendered on both server and client.
          On the server, it will be an empty div with a loading spinner.
          On the client, the `initMap` function will populate it with the Leaflet map.
        -->
        <div ref="mapContainer"
             class="map-container">
          <div v-if="!isMapInitialized"
               class="flex flex-center full-height">
            <q-spinner-dots color="primary"
                            size="40px" />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, shallowRef } from 'vue';
import { QCard, QCardSection, QSpinnerDots } from 'quasar';
import { useRouter } from 'vue-router';

// --- PROPS ---
const props = defineProps({
  currentProperty: {
    type: Object,
    default: () => ({}),
  },
  relevantSoldTransactions: {
    type: Array,
    default: () => [],
  },
  dossierAssetsComparisons: {
    type: Array,
    default: () => [],
  },
  propIdToZoomTo: {
    type: String,
    default: null,
  },
});

// --- STATE ---
const $router = useRouter();
const mapContainer = ref(null);
const leafletMap = ref(null);
const markers = ref({});
const isMapInitialized = ref(false);

// Use shallowRef to hold the Leaflet library object
const L = shallowRef(null);

// --- UTILITY FUNCTIONS ---
const createCustomIcon = (color, iconClass = 'home') => {
  if (!L.value) return null;

  return L.value.divIcon({
    html: `<div style="
          background-color: ${color};
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
        ">
          <i class="material-icons" style="font-size: 16px;">${iconClass}</i>
        </div>`,
    className: 'custom-div-icon',
    iconSize: [30, 30],
    iconAnchor: [15, 15],
    popupAnchor: [0, -15],
  });
};

// Calculate centroid of a polygon for marker placement
const getPolygonCentroid = (coords) => {
  if (!coords?.[0]?.length) return null;

  let xSum = 0;
  let ySum = 0;
  let pointCount = 0;

  coords[0].forEach(([lng, lat]) => {
    xSum += lng;
    ySum += lat;
    pointCount++;
  });

  return pointCount > 0 ? {
    lat: ySum / pointCount,
    lng: xSum / pointCount
  } : null;
};

// --- COMPUTED PROPERTIES ---
const allProperties = computed(() => {
  if (!L.value) {
    return [];
  }

  const properties = [];

  // Handle main property with listing_geo_json.geometry
  if (props.currentProperty?.listing_geo_json.geometry?.type === 'Polygon' &&
    props.currentProperty.listing_geo_json.geometry.coordinates?.[0]?.length) {
    const centroid = getPolygonCentroid(props.currentProperty.listing_geo_json.geometry.coordinates);
    if (centroid) {
      properties.push({
        bgImage: props.currentProperty.sale_listing_pics?.[0]?.image_details?.url || 'https://dummyimage.com/200x100?text=.',
        id: `main_${props.currentProperty.uuid || 'default'}`,
        type: 'main',
        title: props.currentProperty.title || 'Main Property',
        address: props.currentProperty.street_address || 'N/A',
        price: props.currentProperty.formatted_display_price || 'N/A',
        lat: centroid.lat,
        lng: centroid.lng,
        icon: createCustomIcon('#4CAF50', 'home'),
        polygon: props.currentProperty.listing_geo_json.geometry.coordinates[0].map(([lng, lat]) => [lat, lng])
      });
    }
  }

  // Comparison properties (unchanged)
  props.dossierAssetsComparisons.forEach((comparison, index) => {
    if (comparison.right_side_property?.latitude && comparison.right_side_property?.longitude) {
      properties.push({
        bgImage: comparison.right_side_property.sale_listing_pics?.[0]?.image_details?.url || 'https://dummyimage.com/200x100?text=.',
        id: `comp_${comparison.right_side_property.uuid || index}`,
        type: 'comparison',
        title: comparison.right_side_property.title || `Comparison ${index + 1}`,
        address: comparison.right_side_property.street_address || 'N/A',
        price: comparison.right_side_property.formatted_display_price || 'N/A',
        lat: comparison.right_side_property.latitude,
        lng: comparison.right_side_property.longitude,
        icon: createCustomIcon('#2196F3', 'compare_arrows'),
      });
    }
  });

  // Sold transactions (unchanged)
  props.relevantSoldTransactions.forEach((sale, index) => {
    if (sale.st_epc_latitude && sale.st_epc_longitude) {
      properties.push({
        id: `sale_${sale.st_uuid || index}`,
        type: 'sale',
        title: `Recent Sale ${index + 1}`,
        address: sale.st_address || 'N/A',
        price: sale.formatted_sold_price || 'N/A',
        lat: sale.st_epc_latitude,
        lng: sale.st_epc_longitude,
        icon: createCustomIcon('#FF9800', 'sell'),
      });
    }
  });

  return properties;
});

// --- MAP INITIALIZATION ---
const initMap = () => {
  if (!mapContainer.value || !L.value) return;

  leafletMap.value = L.value.map(mapContainer.value).setView([51.505, -0.09], 15);

  L.value.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  }).addTo(leafletMap.value);

  const bounds = L.value.latLngBounds();

  allProperties.value.forEach((property) => {
    // Add marker
    if (property.lat && property.lng) {
      // 11 june 2025 - decided not to show marker for polygon boundary
      // as the lat lng does not reflect true location
      // const marker = L.value.marker([property.lat, property.lng], { icon: property.icon }).addTo(leafletMap.value);
      // const popupContent = `...`; // your popup content logic
      // marker.bindPopup(popupContent);
      // marker.on('click', () => panAndZoomToLocation(property));
      // bounds.extend([property.lat, property.lng]);
      // markers.value[property.id] = marker;
    }

    // Add polygon for main property
    if (property.polygon) {
      L.value.polygon(property.polygon, {
        color: '#4CAF50',
        weight: 2,
        opacity: 0.8,
        fillOpacity: 0.2
      }).addTo(leafletMap.value);
      bounds.extend(property.polygon);
    }
  });

  let propertyToZoomTo = allProperties.value.find(p => p.id === props.propIdToZoomTo);
  if (allProperties.value.length === 1) {
    propertyToZoomTo = allProperties.value[0];
  }
  if (propertyToZoomTo) {
    if (propertyToZoomTo.polygon) {
      leafletMap.value.fitBounds(propertyToZoomTo.polygon, { padding: [50, 50] });
    } else {
      leafletMap.value.setView([propertyToZoomTo.lat, propertyToZoomTo.lng], 14);
    }
    markers.value[propertyToZoomTo.id]?.openPopup();
  } else if (bounds.isValid()) {
    leafletMap.value.fitBounds(bounds, { padding: [50, 50] });
  }

  isMapInitialized.value = true;
};

const panAndZoomToLocation = (property) => {
  if (leafletMap.value) {
    if (property.polygon) {
      leafletMap.value.fitBounds(property.polygon, { padding: [50, 50] });
    } else if (property.lat && property.lng) {
      leafletMap.value.setView([property.lat, property.lng], 17);
    }
    markers.value[property.id]?.openPopup();
  }
  $router.push({ query: { listing: property.id } });
};

// --- LIFECYCLE ---
onMounted(async () => {
  if (typeof window !== 'undefined') {
    try {
      const leaflet = await import('leaflet');
      L.value = leaflet;

      await import('leaflet/dist/leaflet.css');

      delete L.value.Icon.Default.prototype._getIconUrl;
      L.value.Icon.Default.mergeOptions({
        iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
        iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      });

      initMap();
    } catch (e) {
      console.error('Error loading Leaflet:', e);
    }
  }
});
</script>

<style scoped>
.property-map-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.map-container {
  height: 400px;
  width: 100%;
  background-color: #e0e0e0;
  border-radius: 8px;
  z-index: 1;
  position: relative;
}

:deep(.custom-div-icon) {
  background: transparent;
  border: none;
}

:deep(.leaflet-popup-content-wrapper) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.leaflet-popup-content) {
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.leaflet-popup-tip) {
  background: white;
}

:deep(.leaflet-control-zoom) {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.leaflet-control-zoom a) {
  border-radius: 6px;
  color: #333;
  font-weight: bold;
}

:deep(.leaflet-control-attribution) {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  font-size: 11px;
}

:deep(.map-popup-content) {
  width: 200px;
  min-height: 100px;
  background-size: cover;
  background-position: center;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

:deep(.popup-text-scrim) {
  padding: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85) 0%, rgba(0, 0, 0, 0) 100%);
}

:deep(.popup-title) {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.2;
}

:deep(.popup-address),
:deep(.popup-price) {
  margin: 2px 0 0 0;
  font-size: 12px;
  line-height: 1.2;
}
</style>