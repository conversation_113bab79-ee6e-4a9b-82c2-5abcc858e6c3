import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  function getScootForRealtyGames(subdomainName) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/show_games/${subdomainName}`
    return axios.get(apiUrl, {})
  }

  function getScoot(subdomainName) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/show/${subdomainName}`
    return axios.get(apiUrl, {})
  }

  function checkScoot(subdomainName, accessCode) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/check/${subdomainName}/${accessCode}`
    return axios.post(apiUrl, {})
  }

  return {
    getScoot,
    getScootForRealtyGames,
    checkScoot,
  }
}
